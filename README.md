# XBIT Agent

XBIT Agent is a Go-based GraphQL API service for managing user authentication, wallet management, and referral systems for the XBIT DEX platform.

## Features

- **User Management**: Create and manage user accounts with email-based authentication
- **Wallet Integration**: Support for embedded and managed wallet types across multiple chains
- **Referral System**: Multi-level referral tracking with invitation codes
- **GraphQL API**: Modern GraphQL interface for all operations
- **Database Migrations**: Atlas-based database schema management
- **Docker Support**: Containerized deployment with Docker Compose

## Architecture

This project follows the clean architecture pattern inspired by `xbit-goback`:

```
├── cmd/                    # Application entry points
│   ├── graphql/           # GraphQL server
│   └── atlasloader/       # Database schema loader
├── config/                # Configuration structures
├── internal/
│   ├── app/              # Application initialization
│   ├── controller/       # GraphQL controllers and resolvers
│   ├── global/           # Global variables
│   ├── initializer/      # Service initializers
│   ├── model/            # Database models
│   ├── repo/             # Repository layer
│   ├── service/          # Business logic layer
│   └── utils/            # Utility functions
├── migrations/           # Database migrations
└── pkg/                  # Shared packages
```

## Database Schema

### Users Table
- `id` (UUID, Primary Key)
- `email` (TEXT, Unique)
- `is_first_login` (BOOLEAN, Default: true)
- `is_exported_wallet` (BOOLEAN, Default: false)
- `invitation_code` (TEXT, 5-15 characters, initially null)

### User Wallets Table
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key)
- `chain` (TEXT, Not Null)
- `name` (TEXT)
- `wallet_address` (TEXT, Not Null)
- `wallet_id` (UUID)
- `wallet_account_id` (UUID)
- `wallet_type` (TEXT, 'embedded' or 'managed')

### Referrals Table
- `id` (SERIAL, Primary Key)
- `user_id` (UUID, Foreign Key, Unique)
- `referrer_id` (UUID, Foreign Key)
- `depth` (INT, Default: 1)
- `created_at` (TIMESTAMP)

### Referral Snapshots Table
- `user_id` (UUID, Primary Key)
- `direct_count` (INT, Default: 0)
- `total_downline_count` (INT, Default: 0)
- `total_volume_usd` (NUMERIC(38,2), Default: 0)
- `total_rewards_distributed` (NUMERIC(38,6), Default: 0)

## GraphQL API

### Key Mutations

#### Login/Create User
```graphql
mutation Login($input: LoginInput!) {
  login(input: $input) {
    user {
      id
      email
      isFirstLogin
      invitationCode
    }
    token
    isFirstLogin
    message
  }
}
```

#### Create User Wallet
```graphql
mutation CreateUserWallet($input: CreateUserWalletInput!) {
  createUserWallet(input: $input) {
    wallet {
      id
      walletAddress
      chain
      walletType
    }
    success
    message
  }
}
```

### Key Queries

#### Get User Information
```graphql
query GetUser($id: ID!) {
  user(id: $id) {
    id
    email
    wallets {
      id
      chain
      walletAddress
    }
    referralSnapshot {
      directCount
      totalDownlineCount
    }
  }
}
```

#### Get Referral Downlines
```graphql
query GetDownlines($userId: ID!) {
  downlines(userId: $userId) {
    id
    userId
    depth
    user {
      email
    }
  }
}
```

## Getting Started

### Prerequisites

- Go 1.21+
- PostgreSQL 14+
- Atlas CLI (for migrations)

### Installation

1. Clone the repository:
```bash
git clone https://gitlab.ggwp.life/xbit/xbit-dex/xbit-agent.git
cd xbit-agent
```

2. Install dependencies:
```bash
go mod tidy
```

3. Install Atlas CLI:
```bash
# macOS
brew install ariga/tap/atlas

# Linux
curl -sSf https://atlasgo.sh | sh
```

4. Configure database:
```bash
cp config.yaml.example config.yaml
# Edit config.yaml with your database settings
```

5. Run migrations:
```bash
make db-apply
```

6. Build and run:
```bash
make build
./bin/xbit-agent
```

### Docker Deployment

1. Start with Docker Compose:
```bash
docker-compose up -d
```

This will start:
- PostgreSQL database on port 5432
- XBIT Agent API on port 8080

2. Access GraphQL Playground:
```
http://localhost:8080/playground
```

## User Flow

### Login Process

1. **User Login**: When a user logs into XBIT, they provide:
   - Email address
   - Optional: Wallet address and signature
   - Optional: Referrer invitation code

2. **User Creation**: If user doesn't exist:
   - Create new user record with email
   - `invitation_code` is initially null
   - `is_first_login` is set to true
   - If referrer code provided, establish referral relationship

3. **Referral System**:
   - Users can be referred by providing a referrer's invitation code
   - Referral relationships are tracked in the `referrals` table
   - Referral snapshots maintain aggregate statistics

4. **Invitation Code Generation**:
   - Invitation codes are generated on-demand when needed
   - Users start without invitation codes
   - Codes are unique 8-character hex strings

## API Endpoints

- **GraphQL API**: `POST /query`
- **GraphQL Playground**: `GET /playground`
- **Health Check**: `GET /health`

## Configuration

Key configuration options in `config.yaml`:

```yaml
system:
  addr: 8080
  db-type: pgsql

pgsql:
  path: 127.0.0.1
  port: '5432'
  db-name: xbit_agent
  username: postgres
  password: postgres
```

## License

This project is proprietary to XBIT DEX.