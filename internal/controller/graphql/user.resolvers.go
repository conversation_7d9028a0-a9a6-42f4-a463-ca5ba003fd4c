package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
)

// Login is the resolver for the login field.
func (r *mutationResolver) Login(ctx context.Context, input gql_model.LoginInput) (*gql_model.LoginResponse, error) {
	panic(fmt.Errorf("not implemented: Login - login"))
}

// CreateUser is the resolver for the createUser field.
func (r *mutationResolver) CreateUser(ctx context.Context, input gql_model.CreateUserInput) (*gql_model.CreateUserResponse, error) {
	panic(fmt.Errorf("not implemented: CreateUser - createUser"))
}

// CreateUserWallet is the resolver for the createUserWallet field.
func (r *mutationResolver) CreateUserWallet(ctx context.Context, input gql_model.CreateUserWalletInput) (*gql_model.CreateUserWalletResponse, error) {
	panic(fmt.Errorf("not implemented: CreateUserWallet - createUserWallet"))
}

// UpdateFirstLoginStatus is the resolver for the updateFirstLoginStatus field.
func (r *mutationResolver) UpdateFirstLoginStatus(ctx context.Context, userID string) (*gql_model.User, error) {
	panic(fmt.Errorf("not implemented: UpdateFirstLoginStatus - updateFirstLoginStatus"))
}

// UpdateWalletExportStatus is the resolver for the updateWalletExportStatus field.
func (r *mutationResolver) UpdateWalletExportStatus(ctx context.Context, userID string) (*gql_model.User, error) {
	panic(fmt.Errorf("not implemented: UpdateWalletExportStatus - updateWalletExportStatus"))
}

// User is the resolver for the user field.
func (r *queryResolver) User(ctx context.Context, id string) (*gql_model.User, error) {
	panic(fmt.Errorf("not implemented: User - user"))
}

// UserByEmail is the resolver for the userByEmail field.
func (r *queryResolver) UserByEmail(ctx context.Context, email string) (*gql_model.User, error) {
	panic(fmt.Errorf("not implemented: UserByEmail - userByEmail"))
}

// UserWallets is the resolver for the userWallets field.
func (r *queryResolver) UserWallets(ctx context.Context, userID string) ([]*gql_model.UserWallet, error) {
	panic(fmt.Errorf("not implemented: UserWallets - userWallets"))
}

// ReferralInfo is the resolver for the referralInfo field.
func (r *queryResolver) ReferralInfo(ctx context.Context, userID string) (*gql_model.Referral, error) {
	panic(fmt.Errorf("not implemented: ReferralInfo - referralInfo"))
}

// ReferralSnapshot is the resolver for the referralSnapshot field.
func (r *queryResolver) ReferralSnapshot(ctx context.Context, userID string) (*gql_model.ReferralSnapshot, error) {
	panic(fmt.Errorf("not implemented: ReferralSnapshot - referralSnapshot"))
}

// Downlines is the resolver for the downlines field.
func (r *queryResolver) Downlines(ctx context.Context, userID string) ([]*gql_model.Referral, error) {
	panic(fmt.Errorf("not implemented: Downlines - downlines"))
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
