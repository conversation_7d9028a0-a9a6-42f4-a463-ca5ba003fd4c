package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
)

// Login is the resolver for the login field.
func (r *mutationResolver) Login(ctx context.Context, input gql_model.LoginInput) (*gql_model.LoginResponse, error) {
	user, isFirstLogin, err := r.UserService.LoginOrCreateUser(ctx, input.Email, *input.WalletAddress, *input.Signature)
	if err != nil {
		return nil, fmt.Errorf("login failed: %w", err)
	}

	// TODO: Generate JWT token
	token := "dummy-jwt-token"

	return &gql_model.LoginResponse{
		User:         ModelUserToGQL(user),
		Token:        token,
		IsFirstLogin: isFirstLogin,
	}, nil
}

// CreateUser is the resolver for the createUser field.
func (r *mutationResolver) CreateUser(ctx context.Context, input gql_model.CreateUserInput) (*gql_model.CreateUserResponse, error) {
	referrerCode := ""
	if input.ReferrerCode != nil {
		referrerCode = *input.ReferrerCode
	}

	user, err := r.UserService.CreateUser(ctx, input.Email, referrerCode)
	if err != nil {
		return &gql_model.CreateUserResponse{
			User:    nil,
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &gql_model.CreateUserResponse{
		User:    ModelUserToGQL(user),
		Success: true,
		Message: "User created successfully",
	}, nil
}

// CreateUserWallet is the resolver for the createUserWallet field.
func (r *mutationResolver) CreateUserWallet(ctx context.Context, input gql_model.CreateUserWalletInput) (*gql_model.CreateUserWalletResponse, error) {
	userID, err := uuid.Parse(input.UserID)
	if err != nil {
		return &gql_model.CreateUserWalletResponse{
			Wallet:  nil,
			Success: false,
			Message: "Invalid user ID",
		}, nil
	}

	var walletID, walletAccountID *uuid.UUID
	if input.WalletID != nil {
		id, err := uuid.Parse(*input.WalletID)
		if err != nil {
			return &gql_model.CreateUserWalletResponse{
				Wallet:  nil,
				Success: false,
				Message: "Invalid wallet ID",
			}, nil
		}
		walletID = &id
	}

	if input.WalletAccountID != nil {
		id, err := uuid.Parse(*input.WalletAccountID)
		if err != nil {
			return &gql_model.CreateUserWalletResponse{
				Wallet:  nil,
				Success: false,
				Message: "Invalid wallet account ID",
			}, nil
		}
		walletAccountID = &id
	}

	name := ""
	if input.Name != nil {
		name = *input.Name
	}

	walletType := GQLWalletTypeToString(input.WalletType)

	wallet, err := r.UserService.CreateUserWallet(ctx, userID, input.Chain, name, input.WalletAddress, walletID, walletAccountID, walletType)
	if err != nil {
		return &gql_model.CreateUserWalletResponse{
			Wallet:  nil,
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &gql_model.CreateUserWalletResponse{
		Wallet:  ModelUserWalletToGQL(wallet),
		Success: true,
		Message: "Wallet created successfully",
	}, nil
}

// UpdateFirstLoginStatus is the resolver for the updateFirstLoginStatus field.
func (r *mutationResolver) UpdateFirstLoginStatus(ctx context.Context, userID string) (*gql_model.User, error) {
	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	user, err := r.UserService.UpdateFirstLoginStatus(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to update first login status: %w", err)
	}

	return ModelUserToGQL(user), nil
}

// UpdateWalletExportStatus is the resolver for the updateWalletExportStatus field.
func (r *mutationResolver) UpdateWalletExportStatus(ctx context.Context, userID string) (*gql_model.User, error) {
	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	user, err := r.UserService.UpdateWalletExportStatus(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to update wallet export status: %w", err)
	}

	return ModelUserToGQL(user), nil
}

// User is the resolver for the user field.
func (r *queryResolver) User(ctx context.Context, id string) (*gql_model.User, error) {
	userID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	user, err := r.UserService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return ModelUserToGQL(user), nil
}

// UserByEmail is the resolver for the userByEmail field.
func (r *queryResolver) UserByEmail(ctx context.Context, email string) (*gql_model.User, error) {
	user, err := r.UserService.GetUserByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}

	return ModelUserToGQL(user), nil
}

// UserWallets is the resolver for the userWallets field.
func (r *queryResolver) UserWallets(ctx context.Context, userID string) ([]*gql_model.UserWallet, error) {
	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	wallets, err := r.UserService.GetUserWallets(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user wallets: %w", err)
	}

	var gqlWallets []*gql_model.UserWallet
	for _, wallet := range wallets {
		gqlWallets = append(gqlWallets, ModelUserWalletToGQL(&wallet))
	}

	return gqlWallets, nil
}

// ReferralInfo is the resolver for the referralInfo field.
func (r *queryResolver) ReferralInfo(ctx context.Context, userID string) (*gql_model.Referral, error) {
	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	referral, err := r.UserService.GetReferralInfo(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get referral info: %w", err)
	}

	return ModelReferralToGQL(referral), nil
}

// ReferralSnapshot is the resolver for the referralSnapshot field.
func (r *queryResolver) ReferralSnapshot(ctx context.Context, userID string) (*gql_model.ReferralSnapshot, error) {
	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	snapshot, err := r.UserService.GetReferralSnapshot(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get referral snapshot: %w", err)
	}

	return ModelReferralSnapshotToGQL(snapshot), nil
}

// Downlines is the resolver for the downlines field.
func (r *queryResolver) Downlines(ctx context.Context, userID string) ([]*gql_model.Referral, error) {
	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	downlines, err := r.UserService.GetDownlines(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get downlines: %w", err)
	}

	var gqlDownlines []*gql_model.Referral
	for _, downline := range downlines {
		gqlDownlines = append(gqlDownlines, ModelReferralToGQL(&downline))
	}

	return gqlDownlines, nil
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
