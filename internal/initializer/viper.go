package initializer

import (
	"fmt"
	"os"

	"github.com/spf13/viper"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
)

func Viper(path ...string) *viper.Viper {
	var config string

	if len(path) == 0 {
		flag := "config.yaml"
		config = flag
		if configEnv := os.Getenv("GVA_CONFIG"); configEnv != "" {
			config = configEnv
			fmt.Printf("您正在使用GVA_CONFIG环境变量,config的路径为%v\n", config)
		} else {
			fmt.Printf("您正在使用config的默认值,config的路径为%v\n", config)
		}
	} else {
		config = path[0]
		fmt.Printf("您正在使用命令行的值,config的路径为%v\n", config)
	}

	v := viper.New()
	v.SetConfigFile(config)
	v.SetConfigType("yaml")
	err := v.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("Fatal error config file: %s \n", err))
	}
	v.WatchConfig()

	if err = v.Unmarshal(&global.GVA_CONFIG); err != nil {
		panic(err)
	}

	// root 适配性
	// 根据root位置去找到对应迁移位置,保证root路径有效
	global.GVA_CONFIG.Autocode.Root, _ = os.Getwd()

	global.GVA_LOG.Info("config file loaded successfully")
	return v
}
