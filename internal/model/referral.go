package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Referral represents the referrals table
type Referral struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID      uuid.UUID `gorm:"type:uuid;not null;unique;index" json:"user_id"`
	ReferrerID  *uuid.UUID `gorm:"type:uuid;index" json:"referrer_id"`
	Depth       int       `gorm:"default:1" json:"depth"` // 1 means direct referral
	CreatedAt   time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Relationships
	User     User  `gorm:"foreignKey:UserID;references:ID" json:"user"`
	Referrer *User `gorm:"foreignKey:ReferrerID;references:ID" json:"referrer"`
}

// TableName specifies the table name for Referral
func (Referral) TableName() string {
	return "referrals"
}
