package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents the users table
type User struct {
	ID                uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Email             string    `gorm:"type:text;unique" json:"email"`
	IsFirstLogin      bool      `gorm:"default:true" json:"is_first_login"`
	IsExportedWallet  bool      `gorm:"default:false" json:"is_exported_wallet"`
	InvitationCode    string    `gorm:"type:text;size:15" json:"invitation_code"` // Limit 5~15 characters
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"index" json:"deleted_at"`

	// Relationships
	Wallets           []UserWallet       `gorm:"foreignKey:UserID" json:"wallets"`
	Referral          *Referral          `gorm:"foreignKey:UserID" json:"referral"`
	ReferralSnapshot  *ReferralSnapshot  `gorm:"foreignKey:UserID" json:"referral_snapshot"`
	Referrals         []Referral         `gorm:"foreignKey:ReferrerID" json:"referrals"` // Users referred by this user
}

// BeforeCreate will set a UUID rather than numeric ID.
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}
