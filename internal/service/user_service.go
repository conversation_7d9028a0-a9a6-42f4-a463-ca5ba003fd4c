package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gorm.io/gorm"
)

type UserServiceInterface interface {
	CreateUser(ctx context.Context, email, referrerCode string) (*model.User, error)
	LoginOrCreateUser(ctx context.Context, email, walletAddress, signature string) (*model.User, bool, error)
	CreateUserWallet(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID, walletType string) (*model.UserWallet, error)
	UpdateFirstLoginStatus(ctx context.Context, userID uuid.UUID) (*model.User, error)
	UpdateWalletExportStatus(ctx context.Context, userID uuid.UUID) (*model.User, error)
	GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error)
	GetUserByEmail(ctx context.Context, email string) (*model.User, error)
	GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error)
	GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error)
	GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)
	GetDownlines(ctx context.Context, userID uuid.UUID) ([]model.Referral, error)
}

type UserService struct {
	userRepo repo.UserRepositoryInterface
}

func NewUserService() UserServiceInterface {
	return &UserService{
		userRepo: repo.NewUserRepository(),
	}
}

func (s *UserService) CreateUser(ctx context.Context, email, referrerCode string) (*model.User, error) {
	// Check if user already exists
	existingUser, err := s.userRepo.GetByEmail(ctx, email)
	if err == nil && existingUser != nil {
		return nil, fmt.Errorf("user with email %s already exists", email)
	}

	// Generate unique invitation code
	invitationCode, err := s.generateInvitationCode(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate invitation code: %w", err)
	}

	user := &model.User{
		Email:            email,
		IsFirstLogin:     true,
		IsExportedWallet: false,
		InvitationCode:   invitationCode,
	}

	// Create user
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Handle referral if referrer code is provided
	if referrerCode != "" {
		if err := s.createReferralRelationship(ctx, user.ID, referrerCode); err != nil {
			// Log error but don't fail user creation
			// In production, you might want to handle this differently
			fmt.Printf("Failed to create referral relationship: %v\n", err)
		}
	}

	return user, nil
}

func (s *UserService) LoginOrCreateUser(ctx context.Context, email, walletAddress, signature string) (*model.User, bool, error) {
	// Try to get existing user
	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, false, fmt.Errorf("failed to check existing user: %w", err)
	}

	isFirstLogin := false
	
	// If user doesn't exist, create new user
	if err == gorm.ErrRecordNotFound {
		user, err = s.CreateUser(ctx, email, "")
		if err != nil {
			return nil, false, fmt.Errorf("failed to create new user: %w", err)
		}
		isFirstLogin = true
	}

	// TODO: Verify signature if provided
	// This would involve verifying the wallet signature against the message

	return user, isFirstLogin, nil
}

func (s *UserService) CreateUserWallet(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID, walletType string) (*model.UserWallet, error) {
	wallet := &model.UserWallet{
		UserID:          userID,
		Chain:           chain,
		Name:            name,
		WalletAddress:   walletAddress,
		WalletID:        walletID,
		WalletAccountID: walletAccountID,
		WalletType:      walletType,
	}

	if err := s.userRepo.CreateUserWallet(ctx, wallet); err != nil {
		return nil, fmt.Errorf("failed to create user wallet: %w", err)
	}

	return wallet, nil
}

func (s *UserService) UpdateFirstLoginStatus(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	user.IsFirstLogin = false
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

func (s *UserService) UpdateWalletExportStatus(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	user.IsExportedWallet = true
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

func (s *UserService) GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	return s.userRepo.GetByID(ctx, id)
}

func (s *UserService) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	return s.userRepo.GetByEmail(ctx, email)
}

func (s *UserService) GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error) {
	return s.userRepo.GetUserWallets(ctx, userID)
}

func (s *UserService) GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	return s.userRepo.GetReferralInfo(ctx, userID)
}

func (s *UserService) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	return s.userRepo.GetReferralSnapshot(ctx, userID)
}

func (s *UserService) GetDownlines(ctx context.Context, userID uuid.UUID) ([]model.Referral, error) {
	return s.userRepo.GetDownlines(ctx, userID)
}

func (s *UserService) generateInvitationCode(ctx context.Context) (string, error) {
	for attempts := 0; attempts < 10; attempts++ {
		// Generate random bytes
		bytes := make([]byte, 4) // 4 bytes = 8 hex characters
		if _, err := rand.Read(bytes); err != nil {
			return "", err
		}
		
		code := strings.ToUpper(hex.EncodeToString(bytes))
		
		// Check if code already exists
		_, err := s.userRepo.GetByInvitationCode(ctx, code)
		if err == gorm.ErrRecordNotFound {
			return code, nil
		}
		if err != nil {
			return "", err
		}
	}
	
	return "", fmt.Errorf("failed to generate unique invitation code after 10 attempts")
}

func (s *UserService) createReferralRelationship(ctx context.Context, userID uuid.UUID, referrerCode string) error {
	// Find referrer by invitation code
	referrer, err := s.userRepo.GetByInvitationCode(ctx, referrerCode)
	if err != nil {
		return fmt.Errorf("referrer not found: %w", err)
	}

	// Create referral relationship
	referral := &model.Referral{
		UserID:     userID,
		ReferrerID: &referrer.ID,
		Depth:      1, // Direct referral
	}

	if err := s.userRepo.CreateReferral(ctx, referral); err != nil {
		return fmt.Errorf("failed to create referral: %w", err)
	}

	// Initialize referral snapshot for new user
	snapshot := &model.ReferralSnapshot{
		UserID:                  userID,
		DirectCount:             0,
		TotalDownlineCount:      0,
		TotalVolumeUSD:          0,
		TotalRewardsDistributed: 0,
	}

	if err := s.userRepo.UpdateReferralSnapshot(ctx, snapshot); err != nil {
		return fmt.Errorf("failed to create referral snapshot: %w", err)
	}

	return nil
}
